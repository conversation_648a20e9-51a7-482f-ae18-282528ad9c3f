# getLaborBaseDetail 方法实现文档

## 概述

本文档描述了 `getLaborBaseDetail` 方法的实现，该方法用于获取劳动基地的详细信息，包含当前正在劳动的人员列表。

## 实现内容

### 1. 新增实体类

#### ActivityBaseDetailVo
- **路径**: `jeecg-boot-module-system/src/main/java/org/jeecg/modules/lp/activitybase/entity/ActivityBaseDetailVo.java`
- **功能**: 劳动基地详情返回对象
- **主要字段**:
  - 基地基本信息（ID、名称、地址、负责人等）
  - 当前正在劳动的人员数量
  - 当前正在劳动的人员列表

#### WorkingPersonVo（内部类）
- **功能**: 正在劳动的人员信息
- **主要字段**:
  - 姓名
  - 矫正单位
  - 签到时间
  - 联系电话
  - 劳动时长

### 2. Service层实现

#### IActivityBaseService 接口
- **路径**: `jeecg-boot-module-system/src/main/java/org/jeecg/modules/lp/activitybase/service/IActivityBaseService.java`
- **新增方法**: `ActivityBaseDetailVo getLaborBaseDetail(String baseId)`

#### ActivityBaseServiceImpl 实现类
- **路径**: `jeecg-boot-module-system/src/main/java/org/jeecg/modules/lp/activitybase/service/impl/ActivityBaseServiceImpl.java`
- **实现逻辑**:
  1. 参数验证（基地ID不能为空）
  2. 查询劳动基地基本信息
  3. 查询今日已签到但未签退的人员
  4. 计算劳动时长
  5. 组装返回数据

### 3. Controller层实现

#### ActivityBaseController
- **路径**: `jeecg-boot-module-system/src/main/java/org/jeecg/modules/lp/activitybase/controller/ActivityBaseController.java`
- **新增接口**: `GET /activitybase/activityBase/getLaborBaseDetail`
- **参数**: `id` (必填) - 劳动基地ID
- **返回**: 劳动基地详情信息

## 核心查询逻辑

### 查询条件
```sql
SELECT * FROM activity_user_detail 
WHERE base_id = ? 
  AND sign_in_time >= ? (今日开始时间)
  AND sign_up_time IS NULL
```

### 查询说明
- `base_id = ?`: 指定劳动基地
- `sign_in_time >= 今日开始时间`: 今日签到的记录
- `sign_up_time IS NULL`: 尚未签退的记录

## 数据处理

### 时间处理
- **签到时间格式化**: 使用 `yyyy-MM-dd HH:mm:ss` 格式
- **劳动时长计算**: 从签到时间到当前时间的分钟数

### 异常处理
- 参数验证：基地ID为空时抛出 `IllegalArgumentException`
- 数据验证：基地不存在时抛出 `IllegalArgumentException`
- 通用异常：其他异常统一处理并返回错误信息

## API 接口文档

### 请求示例
```http
GET /activitybase/activityBase/getLaborBaseDetail?id=base123
```

### 响应示例
```json
{
  "success": true,
  "message": "操作成功!",
  "code": 200,
  "result": {
    "id": "base123",
    "baseName": "示例劳动基地",
    "jzjgName": "示例矫正单位",
    "address": "示例地址",
    "eventFeature": "活动特色",
    "personCharge": "负责人",
    "telephone": "联系电话",
    "openTime": "08:00",
    "closeTime": "18:00",
    "longitude": "120.123456",
    "latitude": "30.123456",
    "baseType": "1",
    "supportingFacility": "配套设施",
    "currentWorkingCount": 2,
    "currentWorkingPersons": [
      {
        "name": "张三",
        "jzjgName": "示例矫正单位",
        "signInTime": "2025-01-04 09:00:00",
        "phone": "13800138000",
        "duration": "120",
        "userDetailId": "detail123"
      },
      {
        "name": "李四",
        "jzjgName": "示例矫正单位",
        "signInTime": "2025-01-04 10:30:00",
        "phone": "13800138001",
        "duration": "30",
        "userDetailId": "detail124"
      }
    ]
  }
}
```

## 测试

### 单元测试
- **路径**: `jeecg-boot-module-system/src/test/java/org/jeecg/modules/lp/activitybase/service/ActivityBaseServiceTest.java`
- **测试内容**:
  - 参数验证测试
  - 异常情况测试
  - 正常流程测试（需要测试数据）

### 测试建议
1. 准备测试数据：创建测试用的劳动基地和签到记录
2. 测试各种边界情况：空参数、不存在的基地ID等
3. 验证返回数据的完整性和准确性
4. 测试并发访问的安全性

## 注意事项

1. **数据一致性**: 确保签到签退数据的一致性
2. **性能考虑**: 对于大量数据的基地，考虑分页或缓存
3. **权限控制**: 根据业务需求添加相应的权限验证
4. **日志记录**: 重要操作应记录日志便于排查问题
5. **异常处理**: 完善的异常处理机制确保系统稳定性

## 扩展建议

1. **缓存优化**: 对基地基本信息进行缓存
2. **实时更新**: 考虑使用WebSocket推送实时人员变化
3. **统计功能**: 添加历史统计数据
4. **导出功能**: 支持导出当前劳动人员列表
5. **移动端适配**: 提供移动端友好的接口版本
