package org.jeecg.modules.lp.activitybase.service;

import org.jeecg.modules.lp.activitybase.entity.ActivityBaseDetailVo;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * @Description: 劳动基地服务测试
 * @Author: AI Assistant
 * @Date: 2025-01-04
 * @Version: V1.0
 */
@SpringBootTest
@ActiveProfiles("test")
public class ActivityBaseServiceTest {

    @Autowired
    private IActivityBaseService activityBaseService;

    @Test
    public void testGetLaborBaseDetailWithInvalidId() {
        // 测试空ID
        assertThrows(IllegalArgumentException.class, () -> {
            activityBaseService.getLaborBaseDetail(null);
        });

        assertThrows(IllegalArgumentException.class, () -> {
            activityBaseService.getLaborBaseDetail("");
        });
    }

    @Test
    public void testGetLaborBaseDetailWithNonExistentId() {
        // 测试不存在的ID
        assertThrows(IllegalArgumentException.class, () -> {
            activityBaseService.getLaborBaseDetail("non-existent-id");
        });
    }

    // 注意：以下测试需要数据库中有实际的测试数据
    // @Test
    // public void testGetLaborBaseDetailWithValidId() {
    //     // 使用实际存在的基地ID进行测试
    //     String validBaseId = "your-test-base-id";
    //     
    //     ActivityBaseDetailVo result = activityBaseService.getLaborBaseDetail(validBaseId);
    //     
    //     assertNotNull(result);
    //     assertNotNull(result.getId());
    //     assertNotNull(result.getBaseName());
    //     assertNotNull(result.getCurrentWorkingCount());
    //     assertNotNull(result.getCurrentWorkingPersons());
    // }
}
