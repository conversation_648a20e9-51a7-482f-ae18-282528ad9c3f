package org.jeecg.modules.lp.activityuserdetail.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.ccgf.entity.CorrectionObjectBasic;
import org.jeecg.modules.ccgf.service.ICorrectionObjectBasicService;
import org.jeecg.modules.context.LoginUserContext;
import org.jeecg.modules.lp.activitybase.entity.ActivityBase;
import org.jeecg.modules.lp.activitybase.service.IActivityBaseService;
import org.jeecg.modules.lp.activitymanage.entity.ActivityManage;
import org.jeecg.modules.lp.activitymanage.entity.ActivityManageVo;
import org.jeecg.modules.lp.activitymanage.service.IActivityManageService;
import org.jeecg.modules.lp.activitymanage.util.DistanceCalculatorUtil;
import org.jeecg.modules.lp.activitymanage.util.PointsUtil;
import org.jeecg.modules.lp.activityuserdetail.entity.ActivityUserDetail;
import org.jeecg.modules.lp.activityuserdetail.entity.ActivityUserDetailVo;
import org.jeecg.modules.lp.activityuserdetail.entity.CorrectionSyncPublicActivityVo;
import org.jeecg.modules.lp.activityuserdetail.mapper.ActivityUserDetailMapper;
import org.jeecg.modules.lp.activityuserdetail.service.IActivityUserDetailService;
import org.jeecg.modules.oss.entity.OSSFile;
import org.jeecg.modules.oss.service.IOSSFileService;
import org.jeecg.modules.properties.CorrectionRequest;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.entity.SysUserRole;
import org.jeecg.modules.system.service.ISysDepartService;
import org.jeecg.modules.system.service.ISysUserDepartService;
import org.jeecg.modules.system.service.ISysUserRoleService;
import org.jeecg.modules.unifyreceive.enums.RoleEnum;
import org.jeecg.modules.util.HttpClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.Method;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description: 公益活动-矫正对象参与记录
 * @Author: LiuQC
 * @Date: 2024-04-08
 * @Version: V1.0
 */
@Slf4j
@Service
public class ActivityUserDetailServiceImpl extends ServiceImpl<ActivityUserDetailMapper, ActivityUserDetail> implements IActivityUserDetailService {
    @Resource
    private ICorrectionObjectBasicService correctionObjectBasicService;

    @Resource
    private IActivityManageService activityManageService;

    @Resource
    private IOSSFileService iossFileService;

    @Resource
    private IActivityBaseService activityBaseService;

    @Resource
    private ISysUserRoleService sysUserRoleService;

    @Resource
    private ISysUserDepartService sysUserDepartService;

    @Resource
    private ISysDepartService sysDepartService;

    @Value(value = "${sjzx.objectInfo.workNotice}")
    private String url;

    @Resource
    private HttpClient httpClient;

    @Resource
    private CorrectionRequest correctionRequest;

    @Override
    public IPage<ActivityUserDetail> getPage(ActivityUserDetail activityUserDetail, Integer pageNo, Integer pageSize) {
        if (ObjectUtil.isNotEmpty(activityUserDetail.getJzjg())) {
            Set<String> thisLevelAndLowerLevelIds = sysDepartService.getThisLevelAndLowerLevelIds(activityUserDetail.getJzjg());
            activityUserDetail.setJzjg(String.join(",", thisLevelAndLowerLevelIds));
        }
        if (ObjectUtil.isNotEmpty(activityUserDetail.getXm())) {
            activityUserDetail.setXm("*" + activityUserDetail.getXm() + "*");
        }
        QueryWrapper<ActivityUserDetail> queryWrapper = QueryGenerator.initQueryWrapper(activityUserDetail, null);
        Set<String> departPermissionList = LoginUserContext.User.getDepartPermissionList();
        if (CollectionUtil.isNotEmpty(departPermissionList)) {
            queryWrapper.lambda().in(ActivityUserDetail::getJzjg, departPermissionList);
        }
        Page<ActivityUserDetail> page = new Page<ActivityUserDetail>(pageNo, pageSize);
        queryWrapper.lambda().orderByDesc(ActivityUserDetail::getCreateTime);
        IPage<ActivityUserDetail> pageList = this.page(page, queryWrapper);
        List<ActivityUserDetail> records = pageList.getRecords();
        if (CollectionUtil.isNotEmpty(records)) {
            for (ActivityUserDetail record : records) {
                if (ObjectUtil.isNotEmpty(record.getAttachmentId())) {
                    record.setFileList(iossFileService.listByIds(Arrays.asList(record.getAttachmentId().split(","))));
                }
            }
        }
        return pageList;
    }

    @Override
    public void generateActivityByPersonList(ActivityManage activityManage, List<String> sqjzryList) {
        for (String id : sqjzryList) {
            CorrectionObjectBasic correctionObjectBasic = correctionObjectBasicService.getById(id);
            if (ObjectUtil.isNotEmpty(correctionObjectBasic)) {
                ActivityUserDetail activityUserDetail = new ActivityUserDetail();
                activityUserDetail.setJzdxId(correctionObjectBasic.getId());
                activityUserDetail.setActivitiManageId(activityManage.getId());
                activityUserDetail.setXm(correctionObjectBasic.getXm());
                activityUserDetail.setJzjg(correctionObjectBasic.getJzjg());
                activityUserDetail.setJzjgName(correctionObjectBasic.getJzjgName());
                activityUserDetail.setRegistrationTime(DateUtil.date());
                activityUserDetail.setStatus("1");
                this.save(activityUserDetail);
            }

        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> signUp(ActivityManageVo activityManageVo) {
        String activityManageId = null;
        String baseName = null;
        if (activityManageVo.getCreateType() == 0 && ObjectUtil.isNotEmpty(activityManageVo.getBaseId())) {
            String baseId = activityManageVo.getBaseId();
            ActivityBase activityBase = activityBaseService.getById(baseId);
            if (ObjectUtil.isNotEmpty(activityBase)) {
                baseName = activityBase.getBaseName();
            }
            //判断是否已经生成今日的劳动基地活动
            ActivityManage activityManageServiceOne = activityManageService.getOne(new QueryWrapper<ActivityManage>().lambda().eq(ActivityManage::getCreateType, 0).eq(ActivityManage::getBaseId, baseId).ge(ActivityManage::getBeginTime, DateUtil.beginOfDay(DateUtil.date())));
            if (ObjectUtil.isEmpty(activityManageServiceOne)) {
                ActivityManage activityManage = new ActivityManage();
                BeanUtils.copyProperties(activityManageVo, activityManage);
                activityManage.setActPersonNumber(1);
                activityManage.setStatus("1");
                activityManage.setPersonCharge(activityManageVo.getPersonCharge());
                activityManage.setTelephone(activityManageVo.getTelephone());
                activityManageService.save(activityManage);
                activityManageId = activityManage.getId();
            } else {
                if (activityManageServiceOne.getActPersonNumber() >= activityManageServiceOne.getPersonNumber()) {
                    return Result.error("该基地人数已达上限，请选择其他基地！");
                }
                activityManageId = activityManageServiceOne.getId();
                activityManageServiceOne.setActPersonNumber(activityManageServiceOne.getActPersonNumber() + 1);
                activityManageService.updateById(activityManageServiceOne);
            }
        } else {
            activityManageId = activityManageVo.getManageId();
            ActivityManage activityManage = activityManageService.getById(activityManageId);
            if (activityManage.getActPersonNumber() >= activityManage.getPersonNumber()) {
                return Result.error("该基地人数已达上限，请选择其他基地！");
            }
            activityManage.setActPersonNumber(activityManage.getActPersonNumber() + 1);
            activityManageService.updateById(activityManage);
        }

        ActivityUserDetail activityUserDetail = new ActivityUserDetail();
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        activityUserDetail.setJzdxId(loginUser.getId());
        CorrectionObjectBasic correctionObjectBasic = correctionObjectBasicService.getById(loginUser.getId());
        activityUserDetail.setJzjg(correctionObjectBasic.getJzjg());
        activityUserDetail.setGrlxdh(correctionObjectBasic.getDwhm());
        activityUserDetail.setBaseName(baseName);
        activityUserDetail.setBaseId(activityManageVo.getBaseId());
        activityUserDetail.setJzjgName(correctionObjectBasic.getJzjgName());
        activityUserDetail.setRegistrationTime(DateUtil.date());
        activityUserDetail.setXm(correctionObjectBasic.getXm());
        activityUserDetail.setActivitiManageId(activityManageId);
        activityUserDetail.setStatus("1");
        activityUserDetail.setCreateType(1);
        int count = this.count(new QueryWrapper<ActivityUserDetail>().eq("DATE_FORMAT(registration_time , '%Y-%m-%d' )", DateUtil.format(DateUtil.date(), DatePattern.NORM_DATE_PATTERN)).lambda().eq(ActivityUserDetail::getJzdxId, loginUser.getId()).eq(ActivityUserDetail::getBaseId, activityManageVo.getBaseId()));
        if (count > 0) {
            return Result.error("今日已报名该基地，请勿重复报名");
        }
        this.save(activityUserDetail);
        sendJobNotification(activityUserDetail, 1);
        return Result.OK("报名成功！");
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> signIn(ActivityUserDetailVo activityUserDetailVo) {
        ActivityUserDetail userDetail = this.getById(activityUserDetailVo.getUserDetailId());
        if (!"2".equals(userDetail.getStatus())) {
            ActivityManage activityManage = activityManageService.getById(userDetail.getActivitiManageId());
            if (!DateUtil.isIn(DateUtil.date(), activityManage.getBeginTime(), activityManage.getEndTime())) {
                return Result.error("当前不在可签到时间范围内！");
            }
            String distance = DistanceCalculatorUtil.getDistance(activityUserDetailVo.getLatitude(), activityUserDetailVo.getLongitude(), activityManage.getLatitude(), activityManage.getLongitude());
            String replace = activityManage.getAllowRadius().replace("米", "");

            if (Double.parseDouble(distance) > (Double.parseDouble(replace)) / 1000) {
                return Result.error("当前位置不在签到范围内！");
            }
            userDetail.setSignInTime(DateUtil.date());
            userDetail.setSignInAddress(activityUserDetailVo.getAddress());
            userDetail.setStatus("2");
            this.updateById(userDetail);
            sendJobNotification(userDetail, 2);
            return Result.OK("签到成功!");
        }
        return Result.error("签到失败,您已签到!");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> signOut(ActivityUserDetailVo activityUserDetailVo) {
        ActivityUserDetail userDetail = this.getById(activityUserDetailVo.getUserDetailId());
        if (userDetail == null) {
            return Result.error("记录不存在");
        }

        // 判断是否已签到
        if (userDetail.getSignInTime() == null) {
            return Result.error("尚未签到");
        }

        ActivityManage activityManage = activityManageService.getById(userDetail.getActivitiManageId());
        if (activityManage == null) {
            return Result.error("活动信息不存在");
        }

        // 手动创建活动且当前时间早于结束时间，则不允许签退
        if (activityManage.getCreateType() == 1 && !DateUtil.date().isAfter(activityManage.getEndTime())) {
            return Result.error("当前不在可签退时间范围内！");
        }

        // 检查当前位置是否在允许签退范围内
        String distance = DistanceCalculatorUtil.getDistance(
                activityUserDetailVo.getLatitude(),
                activityUserDetailVo.getLongitude(),
                activityManage.getLatitude(),
                activityManage.getLongitude()
        );
        String replace = activityManage.getAllowRadius().replace("米", "");
        if (Double.parseDouble(distance) > (Double.parseDouble(replace)) / 1000) {
            return Result.error("当前位置不在签退范围内！");
        }

        // 签退时间必须距签到时间 >= 1 小时
        long minutes = DateUtil.between(userDetail.getSignInTime(), DateUtil.date(), DateUnit.MINUTE);
        if (minutes < 60) {
            return Result.error("签到未满1小时，不能签退");
        }

        // 更新签退时间和状态
        userDetail.setSignUpTime(DateUtil.date());
        userDetail.setSignUpAddress(activityUserDetailVo.getAddress());
        userDetail.setStatus("3");

        // 设置持续时间（单位：小时）
        long hours = DateUtil.between(userDetail.getSignInTime(), userDetail.getSignUpTime(), DateUnit.HOUR);
        userDetail.setDuration(String.valueOf(hours));

        this.updateById(userDetail);
        sendJobNotification(userDetail, 3);

        return Result.OK("签退成功");
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> eventReflection(ActivityUserDetailVo activityUserDetailVo) {
        ActivityUserDetail userDetail = this.getById(activityUserDetailVo.getUserDetailId());
        userDetail.setEventReflection(activityUserDetailVo.getEventReflection());
        userDetail.setEventScore(activityUserDetailVo.getEventScore());
        List<OSSFile> fileList = activityUserDetailVo.getFileList();
        if (CollectionUtil.isNotEmpty(fileList)) {
            iossFileService.saveBatch(fileList);
            List<String> ids = fileList.stream().map(OSSFile::getId).collect(Collectors.toList());
            userDetail.setAttachmentId(String.join(",", ids));
        }

        userDetail.setStatus("4");
        this.updateById(userDetail);
        return Result.OK("评价成功");
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> workerEvaluation(ActivityUserDetailVo activityUserDetailVo) {
        ActivityUserDetail userDetail = this.getById(activityUserDetailVo.getUserDetailId());
        if ("4".equals(userDetail.getStatus())) {
            userDetail.setWorkEvaluation(activityUserDetailVo.getWorkEvaluation());
            userDetail.setWorkContent(activityUserDetailVo.getWorkContent());
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            userDetail.setWorkUserId(loginUser.getId());
            userDetail.setStatus("5");
            userDetail.setWorkUserName(loginUser.getRealname());
            userDetail.setWorkUserPhone(loginUser.getPhone());
            this.updateById(userDetail);
            return Result.OK("评价成功！");
        }
        return Result.error("评价失败！");
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> userComplaint(ActivityUserDetailVo activityUserDetailVo) {
        Assert.notNull(activityUserDetailVo.getComplaintDescription(), "申诉理由不能为空！");
        ActivityUserDetail userDetail = this.getById(activityUserDetailVo.getUserDetailId());
        if ("5".equals(userDetail.getStatus()) && "2".equals(userDetail.getWorkEvaluation())) {
            userDetail.setComplaintDescription(activityUserDetailVo.getComplaintDescription());
            userDetail.setStatus("6");
            this.updateById(userDetail);
            return Result.OK("提交成功！");
        }
        return Result.error("提交失败！");
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> staffReview(ActivityUserDetail activityUserDetail) {
        Assert.notNull(activityUserDetail.getProcessingResult(), "处理结果不能为空！");
        ActivityUserDetail userDetail = this.getById(activityUserDetail.getId());
        if ("6".equals(userDetail.getStatus())) {
            userDetail.setProcessingResult(activityUserDetail.getProcessingResult());
            userDetail.setProcessingInstruction(activityUserDetail.getProcessingInstruction());
            userDetail.setStatus("7");
            this.updateById(userDetail);
            return Result.OK("审核成功！");
        }
        return Result.error("审核失败！");

    }

    @Override
    public IPage<ActivityUserDetailVo> mobilePersonList(String type, String platitude, String plongitude, Integer pageNo, Integer pageSize) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Page<ActivityUserDetail> page = new Page<>(pageNo, pageSize);
        IPage<ActivityUserDetail> detailIPage = null;
        IPage<ActivityUserDetailVo> activityUserDetailVoPage = new Page<>(pageNo, pageSize);
        List<ActivityUserDetailVo> activityUserDetailVoList = new ArrayList<>();
        if ("1".equals(type)) {
            detailIPage = this.page(page, new QueryWrapper<ActivityUserDetail>().lambda().eq(ActivityUserDetail::getStatus, "1").eq(ActivityUserDetail::getJzdxId, loginUser.getId()).orderByDesc(ActivityUserDetail::getCreateTime));
        }
        if ("2".equals(type)) {
            detailIPage = this.page(page, new QueryWrapper<ActivityUserDetail>().lambda().eq(ActivityUserDetail::getStatus, "2").eq(ActivityUserDetail::getJzdxId, loginUser.getId()).orderByDesc(ActivityUserDetail::getCreateTime));
        }
        if ("3".equals(type)) {
            detailIPage = this.page(page, new QueryWrapper<ActivityUserDetail>().lambda().ne(ActivityUserDetail::getStatus, "1").ne(ActivityUserDetail::getStatus, "2").eq(ActivityUserDetail::getJzdxId, loginUser.getId()).orderByDesc(ActivityUserDetail::getCreateTime));
        }
        List<ActivityUserDetail> records = detailIPage.getRecords();
        if (CollectionUtil.isNotEmpty(records)) {
            for (ActivityUserDetail activityUserDetail : records) {
                ActivityUserDetailVo userDetailVo = activityUserDetailToActivityUserDetailVo(activityUserDetail, platitude, plongitude);
                userDetailVo.setUserDetailId(activityUserDetail.getId());
                userDetailVo.setCreateTime(activityUserDetail.getCreateTime());
                userDetailVo.setUpdateTime(activityUserDetail.getUpdateTime());
                userDetailVo.setXm(maskFirstCharacter(activityUserDetail.getXm()));
                activityUserDetailVoList.add(userDetailVo);
            }
            activityUserDetailVoPage.setRecords(activityUserDetailVoList);
        }
        return activityUserDetailVoPage;
    }

    private static String maskFirstCharacter(String name) {
        if (name == null || name.length() == 0) {
            return name;
        }
        // 将第一个字替换为*
        return "*" + name.substring(1);
    }

    @Override
    public Result<?> possibleToSignOut(ActivityUserDetailVo activityUserDetailVo) {
        HashMap<String, Object> map = new HashMap<>();
        if (ObjectUtil.isEmpty(activityUserDetailVo.getLatitude()) || ObjectUtil.isEmpty(activityUserDetailVo.getLongitude())) {
            map.put("status", false);
            map.put("info", "未获取到定位");
            map.put("msg", "位置不可用");
            return Result.OK(map);
        }
        String addressDetail = PointsUtil.tdtLngAndLatToAddress(Double.parseDouble(activityUserDetailVo.getLongitude()), Double.parseDouble(activityUserDetailVo.getLatitude()));
        ActivityUserDetail userDetail = this.getById(activityUserDetailVo.getUserDetailId());
        ActivityManage activityManage = activityManageService.getById(userDetail.getActivitiManageId());
        if (ObjectUtil.isNotEmpty(userDetail)) {
            if (activityManage.getCreateType() == 1) {
                //待签到
                if (ObjectUtil.isEmpty(userDetail.getSignInTime())) {
                    //未开始
                    if (DateUtil.date().isBefore(activityManage.getBeginTime())) {
                        map.put("status", false);
                        map.put("info", "活动未开始");
                        map.put("msg", "活动未开始");
                        //map.put("addressDetail", addressDetail);
                        return Result.OK(map);
                    }
                    //已结束
                    if (DateUtil.date().isAfter(activityManage.getEndTime())) {
                        map.put("status", false);
                        map.put("info", "活动已结束");
                        map.put("msg", "活动已结束");
                        //map.put("addressDetail", addressDetail);
                        return Result.OK(map);
                    }
                    //进行中
                    String distance = DistanceCalculatorUtil.getDistance(activityUserDetailVo.getLatitude(), activityUserDetailVo.getLongitude(), activityManage.getLatitude(), activityManage.getLongitude());
                    String replace = activityManage.getAllowRadius().replace("米", "");
                    if (Double.parseDouble(distance) > (Double.parseDouble(replace)) / 1000) {
                        map.put("status", false);
                        map.put("info", "活动进行中，请签到");
                        map.put("msg", "未在活动区域内");
                        //map.put("addressDetail", addressDetail);
                        return Result.OK(map);
                    }

                    map.put("status", true);
                    map.put("info", "活动进行中，请签到");
                    map.put("msg", "签到");
                    //map.put("addressDetail", addressDetail);
                    return Result.OK(map);
                }
                if (ObjectUtil.isEmpty(userDetail.getSignUpTime())) {
                    if (!DateUtil.date().isAfter(activityManage.getEndTime())) {
                        map.put("status", false);
                        map.put("info", "活动进行中，等待签退");
                        map.put("msg", "未到结束时间");
                        //map.put("addressDetail", addressDetail);
                        return Result.OK(map);
                    }
                    String distance = DistanceCalculatorUtil.getDistance(activityUserDetailVo.getLatitude(), activityUserDetailVo.getLongitude(), activityManage.getLatitude(), activityManage.getLongitude());
                    String replace = activityManage.getAllowRadius().replace("米", "");
                    if (Double.parseDouble(distance) > (Double.parseDouble(replace)) / 1000) {
                        map.put("status", false);
                        map.put("info", "活动已结束，请签退");
                        map.put("msg", "未在活动区域内");
                        //map.put("addressDetail", addressDetail);
                        return Result.OK(map);
                    }
                    map.put("status", true);
                    map.put("info", "活动已结束，请签到");
                    map.put("msg", "签退");
                    //map.put("addressDetail", addressDetail);
                    return Result.OK(map);
                }
            }
            if (activityManage.getCreateType() == 0) {
                //待签到
                if (ObjectUtil.isEmpty(userDetail.getSignInTime())) {
                    //未开始
                    if (DateUtil.date().isBefore(activityManage.getBeginTime())) {
                        map.put("status", false);
                        map.put("info", "活动未开始");
                        map.put("msg", "活动未开始");
                        //map.put("addressDetail", addressDetail);
                        return Result.OK(map);
                    }
                    //已结束
                    if (DateUtil.date().isAfter(activityManage.getEndTime())) {
                        map.put("status", false);
                        map.put("info", "活动已结束");
                        map.put("msg", "活动已结束");
                        //map.put("addressDetail", addressDetail);
                        return Result.OK(map);
                    }
                    //进行中
                    String distance = DistanceCalculatorUtil.getDistance(activityUserDetailVo.getLatitude(), activityUserDetailVo.getLongitude(), activityManage.getLatitude(), activityManage.getLongitude());
                    String replace = activityManage.getAllowRadius().replace("米", "");
                    if (Double.parseDouble(distance) > (Double.parseDouble(replace)) / 1000) {
                        map.put("status", false);
                        map.put("info", "活动进行中，请签到");
                        map.put("msg", "未在活动区域内");
                        //map.put("addressDetail", addressDetail);
                        return Result.OK(map);
                    }

                    map.put("status", true);
                    map.put("info", "活动进行中，请签到");
                    map.put("msg", "签到");
                    //map.put("addressDetail", addressDetail);
                    return Result.OK(map);
                }
                if (ObjectUtil.isEmpty(userDetail.getSignUpTime())) {
                    long between = DateUtil.between(userDetail.getSignInTime(), DateUtil.date(), DateUnit.MINUTE);
                    if (between < 1) {
                        map.put("status", false);
                        map.put("info", "活动进行中，等待签退");
                        map.put("msg", "未满<br>1小时");
                        //map.put("addressDetail", addressDetail);
                        return Result.OK(map);
                    }
                    String distance = DistanceCalculatorUtil.getDistance(activityUserDetailVo.getLatitude(), activityUserDetailVo.getLongitude(), activityManage.getLatitude(), activityManage.getLongitude());
                    String replace = activityManage.getAllowRadius().replace("米", "");
                    if (Double.parseDouble(distance) > (Double.parseDouble(replace)) / 1000) {
                        map.put("status", false);
                        map.put("info", "活动已结束，请签退");
                        map.put("msg", "未在活动区域内");
                        //map.put("addressDetail", addressDetail);
                        return Result.OK(map);
                    }
                    map.put("status", true);
                    map.put("info", "活动已结束，请签到");
                    //map.put("addressDetail", addressDetail);
                    map.put("msg", "签退");
                    return Result.OK(map);
                }
            }
        }
        map.put("status", false);
        map.put("info", "活动未报名");
        map.put("msg", "待报名");
        //map.put("addressDetail", addressDetail);
        return Result.OK(map);
    }

    @Override
    public ActivityUserDetailVo buildActivityUserDetailVo(ActivityBase activityBase, @NotNull ActivityManage activityManage, @NotNull ActivityUserDetail activityUserDetail) {
        ActivityUserDetailVo activityUserDetailVo = new ActivityUserDetailVo();
        if (ObjectUtil.isNotEmpty(activityBase)) {
            BeanUtils.copyProperties(activityBase, activityUserDetailVo);
            activityUserDetailVo.setBaseId(activityBase.getId());
            if (ObjectUtil.isNotEmpty(activityBase.getPictureIds())) {
                String[] split = activityBase.getPictureIds().split(",");
                List<OSSFile> ossFileList = iossFileService.list(new QueryWrapper<OSSFile>().lambda().in(OSSFile::getId, Arrays.asList(split)));
                activityUserDetailVo.setPictureList(ossFileList);
            }
        }
        BeanUtils.copyProperties(activityManage, activityUserDetailVo);
        activityUserDetailVo.setManageId(activityManage.getId());
        BeanUtils.copyProperties(activityUserDetail, activityUserDetailVo);
        activityUserDetailVo.setUserDetailId(activityUserDetail.getId());

        return activityUserDetailVo;
    }

    @Override
    public ActivityUserDetailVo activityUserDetailToActivityUserDetailVo(ActivityUserDetail activityUserDetail, String platitude, String plongitude) {
        ActivityUserDetailVo activityUserDetailVo = new ActivityUserDetailVo();
        ActivityManage activityManage = activityManageService.getById(activityUserDetail.getActivitiManageId());
        if (ObjectUtil.isNotEmpty(activityManage)) {
            if (ObjectUtil.isNotEmpty(activityManage.getBaseId())) {
                ActivityBase activityBase = activityBaseService.getById(activityManage.getBaseId());
                activityUserDetailVo = buildActivityUserDetailVo(activityBase, activityManage, activityUserDetail);

            } else {
                activityUserDetailVo = buildActivityUserDetailVo(null, activityManage, activityUserDetail);
            }
        } else {
            BeanUtils.copyProperties(activityUserDetail, activityUserDetailVo);
            activityUserDetailVo.setAddress(activityUserDetail.getSignInAddress());
        }

        if (ObjectUtil.isNotEmpty(activityUserDetail.getSignInTime()) && ObjectUtil.isNotEmpty(activityUserDetail.getSignUpTime())) {
            long between = DateUtil.between(activityUserDetail.getSignInTime(), activityUserDetail.getSignUpTime(), DateUnit.MINUTE);
            long hour = between / 60;
            long remainingSeconds = between % 60;
            String duration;
            if (hour > 0) {
                duration = hour + "小时" + remainingSeconds + "分钟";
            } else {
                duration = remainingSeconds + "分钟";
            }
            activityUserDetailVo.setDuration(duration);
        }
        if (ObjectUtil.isNotEmpty(activityUserDetail.getAttachmentId())) {
            String[] strings = activityUserDetail.getAttachmentId().split(",");
            List<OSSFile> ossFileList = iossFileService.list(new QueryWrapper<OSSFile>().lambda().in(OSSFile::getId, Arrays.asList(strings)));
            activityUserDetailVo.setFileList(ossFileList);
        }
        if (ObjectUtil.isNotEmpty(platitude) && ObjectUtil.isNotEmpty(plongitude) && ObjectUtil.isNotEmpty(activityManage)) {
            String distance = DistanceCalculatorUtil.getDistance(platitude, plongitude, activityManage.getLatitude(), activityManage.getLongitude());
            activityUserDetailVo.setDistance(distance);
        }
        return activityUserDetailVo;
    }

    @Override
    public IPage<ActivityUserDetailVo> unfinishList(Page<ActivityUserDetailVo> page, Set<String> userDepartPermissionList) {
        return this.baseMapper.unfinishList(page, userDepartPermissionList);
    }

    @Override
    public void sendJobNotification(ActivityUserDetail activityUserDetail, Integer type) {
        String markdown = null;
        String userIds = null;
        List<SysUser> sysUserList = sysUserDepartService.queryUserByDepId(activityUserDetail.getJzjg());
        if (CollectionUtil.isNotEmpty(sysUserList)) {
            List<String> userIdString = sysUserList.stream().map(SysUser::getId).collect(Collectors.toList());
            List<SysUserRole> list = sysUserRoleService.list(new QueryWrapper<SysUserRole>().lambda().in(SysUserRole::getUserId, userIdString).eq(SysUserRole::getRoleId, RoleEnum.GZTZTXR.getId()));
            if (CollectionUtil.isNotEmpty(list)) {
                List<String> userIdList = list.stream().map(SysUserRole::getUserId).collect(Collectors.toList());
                userIds = String.join(",", userIdList);
            }
        }
        if (ObjectUtil.isNotNull(userIds)) {
            if (type == 1) {
                markdown = buildMarkDownRegistrationNotice(activityUserDetail, userIds);
            }
            if (type == 2) {
                markdown = buildMarkDownSignInNotice(activityUserDetail, userIds);
            }
            if (type == 3) {
                markdown = buildMarkDownSignUpNotice(activityUserDetail, userIds);
            }
            try {
                long l = System.currentTimeMillis();
                String requestTime = String.valueOf(l);
                String appKey = "lI71SEqpKMKfRy3";
                String appSecret = "NjfO5VlhEhtNvUO";
                HttpRequest httpRequest = new HttpRequest(url);
                httpRequest.header("appKey", appKey);
                String sign = DigestUtil.md5Hex(appKey + appSecret + requestTime);
                httpRequest.header("sign", sign);
                httpRequest.header("requestTime", requestTime);
                httpRequest.body(markdown);
                httpRequest.method(Method.POST);
                String body = httpRequest.execute().body();
                log.info("公益活动工作通知返回结果：" + body);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

    }

    private String buildMarkDownRegistrationNotice(ActivityUserDetail activityUserDetail, String userIds) {
        String changeLine = "\n\n";
        String baseId = activityUserDetail.getBaseId();
        ActivityBase activityBase = activityBaseService.getById(baseId);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("title", "报名通知");
        jsonObject.put("userIds", userIds);
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("矫正对象：").append(activityUserDetail.getXm()).append("/").append(activityUserDetail.getJzjgName()).append(changeLine);
        stringBuilder.append("报名时间：").append(activityUserDetail.getRegistrationTime()).append(changeLine);
        stringBuilder.append("劳动基地名称：").append(activityUserDetail.getBaseName()).append(changeLine);
        stringBuilder.append("劳动基地地址：").append(activityBase.getAddress());
        jsonObject.put("message", stringBuilder.toString());
        return jsonObject.toJSONString();
    }

    private String buildMarkDownSignInNotice(ActivityUserDetail activityUserDetail, String userIds) {
        String changeLine = "\n\n";

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("title", "签到通知");
        jsonObject.put("userIds", userIds);
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("矫正对象：").append(activityUserDetail.getXm()).append("/").append(activityUserDetail.getJzjgName()).append(changeLine);
        stringBuilder.append("劳动基地名称：").append(activityUserDetail.getBaseName()).append(changeLine);
        stringBuilder.append("签到时间：").append(activityUserDetail.getSignInTime()).append(changeLine);
        stringBuilder.append("签到地址：").append(activityUserDetail.getSignInAddress());
        jsonObject.put("message", stringBuilder.toString());
        return jsonObject.toJSONString();
    }

    private String buildMarkDownSignUpNotice(ActivityUserDetail activityUserDetail, String userIds) {
        String changeLine = "\n\n";
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("title", "签退通知");
        jsonObject.put("userIds", userIds);
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("矫正对象：").append(activityUserDetail.getXm()).append("/").append(activityUserDetail.getJzjgName()).append(changeLine);
        stringBuilder.append("劳动基地名称：").append(activityUserDetail.getBaseName()).append(changeLine);
        stringBuilder.append("签退时间：").append(activityUserDetail.getSignUpTime()).append(changeLine);
        stringBuilder.append("签退地址：").append(activityUserDetail.getSignUpAddress());
        jsonObject.put("message", stringBuilder.toString());
        return jsonObject.toJSONString();
    }

    @Override
    public ActivityBase calculateBaseScore(String id) {
        return this.baseMapper.calculateBaseScore(id);
    }

    @Override
    public Result<?> autoReview(ActivityUserDetail activityUserDetail) {
        //审核成功后上传省平台
        if ("7".equals(activityUserDetail.getStatus())) {
            CorrectionSyncPublicActivityVo correctionSyncPublicActivityVo = new CorrectionSyncPublicActivityVo();
            BeanUtils.copyProperties(activityUserDetail, correctionSyncPublicActivityVo);
            correctionSyncPublicActivityVo.setSqfwjssj(DateUtil.format(activityUserDetail.getSignUpTime(), DatePattern.UTC_MS_PATTERN));
            correctionSyncPublicActivityVo.setSqfwkssj(DateUtil.format(activityUserDetail.getSignInTime(), DatePattern.UTC_MS_PATTERN));
            correctionSyncPublicActivityVo.setJlr(activityUserDetail.getUpdateBy());
            correctionSyncPublicActivityVo.setSqfwnr(activityUserDetail.getEventReflection());
            correctionSyncPublicActivityVo.setJiedaoId(activityUserDetail.getJzjg());
            correctionSyncPublicActivityVo.setSqfwdd(activityUserDetail.getSignInAddress());
            correctionSyncPublicActivityVo.setSqfwsc(activityUserDetail.getDuration());
            if (ObjectUtil.isNotEmpty(activityUserDetail.getAttachmentId())) {
                String[] strings = activityUserDetail.getAttachmentId().split(",");
                List<OSSFile> ossFileList = iossFileService.list(new QueryWrapper<OSSFile>().lambda().in(OSSFile::getId, Arrays.asList(strings)));
                correctionSyncPublicActivityVo.setFjUrl(ossFileList.stream().map(OSSFile::getUrl).collect(Collectors.toList()));
            }
            log.info(JSONObject.toJSONString(correctionSyncPublicActivityVo));
            JSONObject jsonObject = httpClient.PostRequest(correctionRequest.getPublicActivityAdd(), correctionSyncPublicActivityVo);
            if (jsonObject.getInteger("code") != 0) {
                return Result.error("上传省平台失败");
            } else {
                //上传成果后继续上传参加人员
                JSONObject personObject = new JSONObject();
                personObject.put("gyldId", correctionSyncPublicActivityVo.getId());
                personObject.put("pid", activityUserDetail.getJzdxId());
                personObject.put("sqfwbx", activityUserDetail.getProcessingInstruction());
                httpClient.PostRequest(correctionRequest.getPublicActivityPersonAdd(), personObject);
            }
        }
        this.updateById(activityUserDetail);
        return Result.OK("审核成功");
    }

    @Override
    public Result<?> autoReviewUpload(ActivityUserDetail activityUserDetail) {
        if ("7".equals(activityUserDetail.getStatus())) {
            return updateActivityToZfba(activityUserDetail);
        }
        return Result.OK("上报成功！");
    }

    private Result<?> updateActivityToZfba(ActivityUserDetail activityUserDetail) {
        CorrectionSyncPublicActivityVo correctionSyncPublicActivityVo = new CorrectionSyncPublicActivityVo();
        BeanUtils.copyProperties(activityUserDetail, correctionSyncPublicActivityVo);
        correctionSyncPublicActivityVo.setSqfwjssj(DateUtil.format(activityUserDetail.getSignUpTime(), DatePattern.UTC_MS_PATTERN));
        correctionSyncPublicActivityVo.setSqfwkssj(DateUtil.format(activityUserDetail.getSignInTime(), DatePattern.UTC_MS_PATTERN));
        correctionSyncPublicActivityVo.setJlr(activityUserDetail.getUpdateBy());
        correctionSyncPublicActivityVo.setSqfwnr(activityUserDetail.getEventReflection());
        correctionSyncPublicActivityVo.setJiedaoId(activityUserDetail.getJzjg());
        correctionSyncPublicActivityVo.setSqfwdd(activityUserDetail.getSignInAddress());
        correctionSyncPublicActivityVo.setSqfwsc(activityUserDetail.getDuration());
        if (ObjectUtil.isNotEmpty(activityUserDetail.getAttachmentId())) {
            String[] strings = activityUserDetail.getAttachmentId().split(",");
            List<OSSFile> ossFileList = iossFileService.list(new QueryWrapper<OSSFile>().lambda().in(OSSFile::getId, Arrays.asList(strings)));
            correctionSyncPublicActivityVo.setFjUrl(ossFileList.stream().map(OSSFile::getUrl).collect(Collectors.toList()));
        }
        log.info(JSONObject.toJSONString(correctionSyncPublicActivityVo));
        JSONObject jsonObject = httpClient.PostRequest(correctionRequest.getPublicActivityAdd(), correctionSyncPublicActivityVo);
        if (jsonObject.getInteger("code") != 0) {
            activityUserDetail.setZfbaStatus("2");
            activityUserDetail.setZfbaResult(jsonObject.toJSONString());
            this.updateById(activityUserDetail);
            return Result.error("上传省平台失败");
        } else {
            //上传成果后继续上传参加人员
            JSONObject personObject = new JSONObject();
            personObject.put("gyldId", correctionSyncPublicActivityVo.getId());
            personObject.put("pid", activityUserDetail.getJzdxId());
            personObject.put("sqfwbx", activityUserDetail.getProcessingInstruction());
            JSONObject result = httpClient.PostRequest(correctionRequest.getPublicActivityPersonAdd(), personObject);
            activityUserDetail.setZfbaStatus("1");
            activityUserDetail.setZfbaResult(result.toJSONString());
            this.updateById(activityUserDetail);
        }
        return Result.OK("上报成功！");
    }

    /**
     * 上传公益活动到执法办案定时任务
     */
//    @Scheduled(cron = "0 20 * * * ? ")
    public void uploadActivityToZfba() {
        //查询通过活动报名，签退成功，且未上传执法办案的记录
        List<ActivityUserDetail> list = this.list(new QueryWrapper<ActivityUserDetail>().lambda().eq(ActivityUserDetail::getStatus, "1").isNull(ActivityUserDetail::getZfbaStatus));
        if (CollectionUtil.isNotEmpty(list)) {
            for (ActivityUserDetail activityUserDetail : list) {
                updateActivityToZfba(activityUserDetail);
            }
        }

    }


    @Scheduled(cron = "0 1/2 * * * ? ")
    public void updateStatus() {

        List<ActivityUserDetail> list = this.list(new QueryWrapper<ActivityUserDetail>().lambda().eq(ActivityUserDetail::getStatus, "1"));
        if (CollectionUtil.isNotEmpty(list)) {
            for (ActivityUserDetail activityUserDetail : list) {
                ActivityManage activityManage = activityManageService.getById(activityUserDetail.getActivitiManageId());
                if (ObjectUtil.isNotEmpty(activityManage)) {
                    if (DateUtil.date().isAfter(activityManage.getEndTime())) {
                        activityUserDetail.setStatus("9");
                        this.updateById(activityUserDetail);
                    }
                }
            }
        }
    }

    @Override
    public ActivityUserDetailVo mobilePersonDetail(String id, String platitude, String plongitude) {
        ActivityUserDetail activityUserDetail = this.getById(id);
        ActivityUserDetailVo userDetailVo = activityUserDetailToActivityUserDetailVo(activityUserDetail, platitude, plongitude);
        userDetailVo.setUserDetailId(activityUserDetail.getId());
        userDetailVo.setCreateTime(activityUserDetail.getCreateTime());
        userDetailVo.setUpdateTime(activityUserDetail.getUpdateTime());
        userDetailVo.setXm(maskFirstCharacter(activityUserDetail.getXm()));
        return userDetailVo;
    }

    @Override
    public Result<?> updateEvaluationResult(String id, String evaluationResult) {
        ActivityUserDetail activityUserDetail = this.getById(id);
        if (ObjectUtil.isEmpty(activityUserDetail)) {
            return Result.error("记录不存在");
        }
        //评价结果只能调整1次
        if (ObjectUtil.isNotEmpty(activityUserDetail.getAdjustCount()) && activityUserDetail.getAdjustCount() >= 1) {
            return Result.error("评价结果只能调整1次");
        }
        activityUserDetail.setAdjustCount(activityUserDetail.getAdjustCount() + 1);
        activityUserDetail.setProcessResult(evaluationResult);
        this.updateById(activityUserDetail);
        return Result.OK("调整评价结果成功");
    }
}
