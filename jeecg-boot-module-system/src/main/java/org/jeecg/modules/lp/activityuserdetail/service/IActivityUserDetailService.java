package org.jeecg.modules.lp.activityuserdetail.service;

import java.util.List;
import java.util.Set;

import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.lp.activitybase.entity.ActivityBase;
import org.jeecg.modules.lp.activitymanage.entity.ActivityManage;
import org.jeecg.modules.lp.activitymanage.entity.ActivityManageVo;
import org.jeecg.modules.lp.activityuserdetail.entity.ActivityUserDetail;
import org.jeecg.modules.lp.activityuserdetail.entity.ActivityUserDetailVo;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: 公益活动-矫正对象参与记录
 * @Author: LiuQC
 * @Date:   2024-04-08
 * @Version: V1.0
 */
public interface IActivityUserDetailService extends IService<ActivityUserDetail> {

    /**
     * 生成矫正对象公益活动参与记录
     * @param activityManage
     * @param sqjzryList
     */
    void generateActivityByPersonList(ActivityManage activityManage, List<String> sqjzryList);

    /**
     * 矫正对象报名
     * @param activityManageVo
     * @return
     */
    Result<?> signUp(ActivityManageVo activityManageVo);

    /**
     * 矫正对象签到
     * @param activityUserDetailVo
     * @return
     */
    Result<?> signIn(ActivityUserDetailVo activityUserDetailVo);

    /**
     * 矫正对象签退
     * @param activityUserDetailVo
     * @return
     */
    Result<?> signOut(ActivityUserDetailVo activityUserDetailVo);

    /**
     * 矫正对象填写活动感想
     * @param activityUserDetailVo
     * @return
     */
    Result<?> eventReflection(ActivityUserDetailVo activityUserDetailVo);

    /**
     * 负责人评价
     * @param activityUserDetailVo
     * @return
     */
    Result<?> workerEvaluation(ActivityUserDetailVo activityUserDetailVo);

    /**
     * 矫正对象申诉
     * @param activityUserDetailVo
     * @return
     */
    Result<?> userComplaint(ActivityUserDetailVo activityUserDetailVo);

    /**
     * 工作人员审核
     * @param activityUserDetail
     * @return
     */
    Result<?> staffReview(ActivityUserDetail activityUserDetail);

    /**
     * 构建移动端用户详细信息
     * @param activityBase
     * @param activityManage
     * @param activityUserDetail
     * @return
     */
    ActivityUserDetailVo buildActivityUserDetailVo(ActivityBase activityBase, ActivityManage activityManage,ActivityUserDetail activityUserDetail);

    /**
     * 丰富用户详情
     * @param activityUserDetail
     * @param platitude
     * @param plongitude
     * @return
     */
    ActivityUserDetailVo activityUserDetailToActivityUserDetailVo(ActivityUserDetail activityUserDetail,String platitude,String plongitude);

    /**
     * 移动端我的活动分页
     * @param type
     * @param pageSize
     * @return
     */
    IPage<ActivityUserDetailVo> mobilePersonList(String type,String platitude,String plongitude,Integer pageNo, Integer pageSize);

    /**
     * 判断是否能签退
     * @param activityUserDetailVo
     * @return
     */
    Result<?> possibleToSignOut(ActivityUserDetailVo activityUserDetailVo);

    /**
     * 未完成用户
     * @param userDepartPermissionList
     * @return
     */
    IPage<ActivityUserDetailVo> unfinishList(Page<ActivityUserDetailVo> page, Set<String> userDepartPermissionList);

    /**
     * 发送工作通知
     * @param activityUserDetail
     * @param type 1-活动报名通知；2-活动签到通知；3-活动签退通知；
     */
    void sendJobNotification(ActivityUserDetail activityUserDetail,Integer type);

    /**
     * 计算基地得分
     * @param id
     * @return
     */
    ActivityBase calculateBaseScore(String id);

    /**
     * 自主上报审核
     * @param activityUserDetail
     * @return
     */
    Result<?> autoReview(ActivityUserDetail activityUserDetail);

    /**
     * 统一分页
     * @param activityUserDetail
     * @param pageNo
     * @param pageSize
     * @return
     */
    IPage<ActivityUserDetail> getPage(ActivityUserDetail activityUserDetail, Integer pageNo, Integer pageSize);

    /**
     * 手动上报省平台
     * @param activityUserDetail
     * @return
     */
    Result<?> autoReviewUpload(ActivityUserDetail activityUserDetail);

    ActivityUserDetailVo mobilePersonDetail(String id,String latitude,String longitude);

    /**
     * 调整评价结果
     * @param id
     * @param evaluationResult
     * @return
     */
    Result<?> updateEvaluationResult(String id, String evaluationResult);
}
