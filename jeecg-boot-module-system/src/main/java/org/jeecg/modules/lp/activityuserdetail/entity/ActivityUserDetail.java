package org.jeecg.modules.lp.activityuserdetail.entity;

import java.util.List;

import org.jeecg.common.aspect.annotation.Dict;
import org.jeecg.modules.oss.entity.OSSFile;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 公益活动-矫正对象参与记录
 * @Author: LiuQC
 * @Date: 2024-04-08
 * @Version: V1.0
 */
@Data
@TableName("activity_user_detail")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "activity_user_detail对象", description = "公益活动-矫正对象参与记录")
public class ActivityUserDetail {

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
    /**
     * 矫正对象id
     */
    @Excel(name = "矫正对象id", width = 15)
    @ApiModelProperty(value = "矫正对象id")
    private java.lang.String jzdxId;

    /**
     * 姓名
     */
    @Excel(name = "姓名", width = 15)
    @ApiModelProperty(value = "姓名")
    private java.lang.String xm;

    /**
     * 个人联系电话
     */
    @ApiModelProperty(value = "个人联系电话")
    private String grlxdh;
    /**
     * 矫正单位id
     */
    @ApiModelProperty(value = "矫正单位id")
    private java.lang.String jzjg;

    /**
     * 矫正单位
     */
    @Excel(name = "矫正单位", width = 15)
    @ApiModelProperty(value = "矫正单位")
    private java.lang.String jzjgName;
    /**
     * 公益活动id
     */
    @Excel(name = "公益活动id", width = 15)
    @ApiModelProperty(value = "公益活动id")
    private java.lang.String activitiManageId;

    /**
     * 基地名称
     */
    @ApiModelProperty(value = "基地名称")
    private String baseName;

    /**
     * 基地id
     */
    @ApiModelProperty(value = "基地id")
    private String baseId;
    /**
     * 公益活动形式
     */
    @Excel(name = "公益活动形式", width = 15)
    @ApiModelProperty(value = "公益活动形式")
    @Dict(dicCode = "PubWelfareActivitie.activityForm")
    private java.lang.String activitiType;

    /**
     * 活动类型
     */
    @Excel(name = "活动类型", width = 15)
    @ApiModelProperty(value = "活动类型")
    @Dict(dicCode = "PubWelfareActivitie.activityType")
    private java.lang.String activitiBelong;

    /**
     * 活动内容
     */
    @Excel(name = "活动内容", width = 15)
    @ApiModelProperty(value = "活动内容")
    @Dict(dicCode = "PubWelfareActivitie.activityContent")
    private java.lang.String activitiContent;

    /**
     * 报名时间
     */
    @Excel(name = "报名时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "报名时间")
    private java.util.Date registrationTime;
    /**
     * 签到时间
     */
    @Excel(name = "签到时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "签到时间")
    private java.util.Date signInTime;
    /**
     * 签到地点
     */
    @Excel(name = "签到地点", width = 15)
    @ApiModelProperty(value = "签到地点")
    private java.lang.String signInAddress;
    /**
     * 签退时间
     */
    @Excel(name = "签退时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "签退时间")
    private java.util.Date signUpTime;

    /**
     * 签退地点
     */
    @Excel(name = "签退地点", width = 15)
    @ApiModelProperty(value = "签退地点")
    private java.lang.String signUpAddress;
    /**
     * 时长
     */
    @Excel(name = "时长", width = 15)
    @ApiModelProperty(value = "时长")
    private java.lang.String duration;
    /**
     * 活动感想
     */
    @Excel(name = "活动感想", width = 15)
    @ApiModelProperty(value = "活动感想")
    private java.lang.String eventReflection;
    /**
     * 评分
     */
    @Excel(name = "评分", width = 15)
    @ApiModelProperty(value = "评分")
    private java.lang.Integer eventScore;
    /**
     * 负责人评价（1-合格；2-不合格）
     */
    @Excel(name = "负责人评价（1-合格；2-不合格）", width = 15)
    @ApiModelProperty(value = "负责人评价（1-合格；2-不合格）")
    private java.lang.String workEvaluation;
    /**
     * 负责人评价内容
     */
    @Excel(name = "负责人评价内容", width = 15)
    @ApiModelProperty(value = "负责人评价内容")
    private java.lang.String workContent;

    /**
     * 负责人id
     */
    @ApiModelProperty(value = "负责人id")
    private String workUserId;

    /**
     * 负责人姓名
     */
    @ApiModelProperty(value = "负责人姓名")
    private String workUserName;

    /**
     * 负责人电话
     */
    @ApiModelProperty(value = "负责人电话")
    private String workUserPhone;

    /**
     * 申诉说明
     */
    @ApiModelProperty(value = "申诉说明")
    private String complaintDescription;

    /**
     * 处理结果（Y-申诉成功；N-申诉失败）
     */
    @ApiModelProperty(value = "处理结果（Y-申诉成功；N-申诉失败）")
    private String processingResult;
    /**
     * 处理说明
     */
    @ApiModelProperty(value = "处理说明")
    private String processingInstruction;
    /**
     * 附件id
     */
    @Excel(name = "附件id", width = 15)
    @ApiModelProperty(value = "附件id")
    private java.lang.String attachmentId;
    /**
     * 状态（1-已报名；2-已签到；3-已签退；4-已评价；5-工作人员已审核；6-矫正对象已申诉；7-申诉结果已处理;9-超时未处理),自主上报（6-待审核，7-已通过，8-已退回）
     */
    @Excel(name = "状态（1-已报名；2-已签到；3-已签退；4-已评价；5-工作人员已审核；6-矫正对象已申诉；7-申诉结果已处理)", width = 15)
    @ApiModelProperty(value = "状态（1-已报名；2-已签到；3-已签退；4-已评价；5-工作人员已审核；6-矫正对象已申诉；7-申诉结果已处理)")
    private java.lang.String status;

    /**
     * 处理结果（1-通过，2-不通过）
     */
    @Excel(name = "处理结果", width = 15)
    @ApiModelProperty(value = "处理结果")
    private java.lang.String processResult;

    /**
     * 调整次数
     */
    @ApiModelProperty(value = "调整次数")
    private java.lang.Integer adjustCount;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
    /**
     * createBy
     */
    @Excel(name = "createBy", width = 15)
    @ApiModelProperty(value = "createBy")
    private java.lang.String createBy;
    /**
     * updateTime
     */
    @Excel(name = "updateTime", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "updateTime")
    private java.util.Date updateTime;
    /**
     * updateBy
     */
    @Excel(name = "updateBy", width = 15)
    @ApiModelProperty(value = "updateBy")
    private java.lang.String updateBy;
    /**
     * 纬度
     */
    @TableField(exist = false)
    private String latitude;
    /**
     * 经度
     */
    @TableField(exist = false)
    private String longitude;
    /**
     * 创建类型（1-活动报名；2-自主上报）,默认活动报名
     */
    private Integer createType;

    /**
     * 附件列表
     */
    @TableField(exist = false)
    private List<OSSFile> fileList;

    /**
     * 上传执法办案状态
     */
    private String zfbaStatus;

    /**
     * 上传执法办案返回结果
     */
    private String zfbaResult;
}
