package org.jeecg.modules.lp.activitybase.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.jeecg.modules.lp.activitybase.entity.ActivityBase;
import org.jeecg.modules.lp.activitybase.entity.ActivityBaseDetailVo;
import org.jeecg.modules.lp.activitybase.mapper.ActivityBaseMapper;
import org.jeecg.modules.lp.activitybase.service.IActivityBaseService;
import org.jeecg.modules.lp.activitymanage.entity.ActivityManage;
import org.jeecg.modules.lp.activitymanage.mapper.ActivityManageMapper;
import org.jeecg.modules.lp.activityuserdetail.entity.ActivityUserDetail;
import org.jeecg.modules.lp.activityuserdetail.service.IActivityUserDetailService;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;

/**
 * @Description: 公益活动-劳动基地
 * @Author: LiuQC
 * @Date: 2024-04-08
 * @Version: V1.0
 */
@Service
public class ActivityBaseServiceImpl extends ServiceImpl<ActivityBaseMapper, ActivityBase> implements IActivityBaseService {

    @Resource
    private ActivityManageMapper activityManageMapper;

    @Resource
    private IActivityUserDetailService activityUserDetailService;

    @Scheduled(cron = "0 54 * * * ?")
    @Override
    public void updateActivityBaseCount() {
        try {
            //批量更新
            List<ActivityBase> list = this.list();
            for (ActivityBase activityBase : list) {
                List<ActivityManage> activityManageList = activityManageMapper.selectList(new QueryWrapper<ActivityManage>().lambda().eq(ActivityManage::getBaseId, activityBase.getId()));
                if (CollectionUtil.isNotEmpty(activityManageList)) {
                    activityBase.setLaunchNumber(activityManageList.size());
                    int sum = activityManageList.stream().mapToInt(ActivityManage::getActPersonNumber).sum();
                    activityBase.setTotalParticipate(sum);
                } else {
                    activityBase.setLaunchNumber(0);
                    activityBase.setTotalParticipate(0);
                }
                this.updateById(activityBase);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Override
    public ActivityBaseDetailVo getLaborBaseDetail(String baseId) {
        // 参数验证
        if (ObjectUtil.isEmpty(baseId)) {
            throw new IllegalArgumentException("劳动基地ID不能为空");
        }

        // 查询劳动基地基本信息
        ActivityBase activityBase = this.getById(baseId);
        if (ObjectUtil.isEmpty(activityBase)) {
            throw new IllegalArgumentException("劳动基地不存在");
        }

        // 创建返回对象
        ActivityBaseDetailVo detailVo = new ActivityBaseDetailVo();

        // 复制基本信息
        BeanUtils.copyProperties(activityBase, detailVo);

        // 查询今日已签到但未签退的人员（当前正在劳动的人员）
        Date todayStart = DateUtil.beginOfDay(DateUtil.date());
        QueryWrapper<ActivityUserDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(ActivityUserDetail::getBaseId, baseId)
                .ge(ActivityUserDetail::getSignInTime, todayStart)
                .isNull(ActivityUserDetail::getSignUpTime);

        List<ActivityUserDetail> workingPersons = activityUserDetailService.list(queryWrapper);

        // 设置当前劳动人员数量
        detailVo.setCurrentWorkingCount(workingPersons.size());

        // 转换为VO对象列表
        List<ActivityBaseDetailVo.WorkingPersonVo> workingPersonVos = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(workingPersons)) {
            for (ActivityUserDetail userDetail : workingPersons) {
                ActivityBaseDetailVo.WorkingPersonVo personVo = new ActivityBaseDetailVo.WorkingPersonVo();
                personVo.setName(userDetail.getXm());
                personVo.setJzjgName(userDetail.getJzjgName());
                personVo.setPhone(userDetail.getGrlxdh());
                personVo.setUserDetailId(userDetail.getId());

                // 格式化签到时间
                if (ObjectUtil.isNotEmpty(userDetail.getSignInTime())) {
                    personVo.setSignInTime(DateUtil.format(userDetail.getSignInTime(), DatePattern.NORM_DATETIME_PATTERN));
                }

                // 计算劳动时长（从签到时间到现在的分钟数）
                if (ObjectUtil.isNotEmpty(userDetail.getSignInTime())) {
                    long durationMinutes = DateUtil.betweenMs(userDetail.getSignInTime(), DateUtil.date()) / (1000 * 60);
                    personVo.setDuration(String.valueOf(durationMinutes));
                }

                workingPersonVos.add(personVo);
            }
        }

        detailVo.setCurrentWorkingPersons(workingPersonVos);

        return detailVo;
    }
}
