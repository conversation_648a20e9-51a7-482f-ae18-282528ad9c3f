package org.jeecg.modules.lp.activitybase.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecg.modules.lp.activityuserdetail.entity.ActivityUserDetail;

import java.util.List;

/**
 * @Description: 劳动基地详情VO
 * @Author: AI Assistant
 * @Date: 2025-01-04
 * @Version: V1.0
 */
@Data
@ApiModel(value = "ActivityBaseDetailVo", description = "劳动基地详情VO")
public class ActivityBaseDetailVo {

    /**
     * 基地ID
     */
    @ApiModelProperty(value = "基地ID")
    private String id;

    /**
     * 劳动基地名称
     */
    @ApiModelProperty(value = "劳动基地名称")
    private String baseName;

    /**
     * 矫正单位
     */
    @ApiModelProperty(value = "矫正单位")
    private String jzjgName;

    /**
     * 地址
     */
    @ApiModelProperty(value = "地址")
    private String address;

    /**
     * 活动特色
     */
    @ApiModelProperty(value = "活动特色")
    private String eventFeature;

    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    private String personCharge;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    private String telephone;

    /**
     * 开放时间-开始
     */
    @ApiModelProperty(value = "开放时间-开始")
    private String openTime;

    /**
     * 开放时间-结束
     */
    @ApiModelProperty(value = "开放时间-结束")
    private String closeTime;

    /**
     * 经度
     */
    @ApiModelProperty(value = "经度")
    private String longitude;

    /**
     * 纬度
     */
    @ApiModelProperty(value = "纬度")
    private String latitude;

    /**
     * 基地类别
     */
    @ApiModelProperty(value = "基地类别")
    private String baseType;

    /**
     * 配套设施
     */
    @ApiModelProperty(value = "配套设施")
    private String supportingFacility;

    /**
     * 当前正在劳动的人员数量
     */
    @ApiModelProperty(value = "当前正在劳动的人员数量")
    private Integer currentWorkingCount;

    /**
     * 当前正在劳动的人员列表
     */
    @ApiModelProperty(value = "当前正在劳动的人员列表")
    private List<WorkingPersonVo> currentWorkingPersons;

    /**
     * 正在劳动的人员信息VO
     */
    @Data
    @ApiModel(value = "WorkingPersonVo", description = "正在劳动的人员信息VO")
    public static class WorkingPersonVo {
        
        /**
         * 姓名
         */
        @ApiModelProperty(value = "姓名")
        private String name;

        /**
         * 矫正单位
         */
        @ApiModelProperty(value = "矫正单位")
        private String jzjgName;

        /**
         * 签到时间
         */
        @ApiModelProperty(value = "签到时间")
        private String signInTime;

        /**
         * 联系电话
         */
        @ApiModelProperty(value = "联系电话")
        private String phone;

        /**
         * 劳动时长（分钟）
         */
        @ApiModelProperty(value = "劳动时长（分钟）")
        private String duration;

        /**
         * 活动详情ID
         */
        @ApiModelProperty(value = "活动详情ID")
        private String userDetailId;
    }
}
