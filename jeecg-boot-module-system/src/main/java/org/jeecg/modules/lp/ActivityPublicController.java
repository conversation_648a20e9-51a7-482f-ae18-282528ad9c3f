package org.jeecg.modules.lp;

import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.modules.lp.activitybase.service.IActivityBaseService;
import org.jeecg.modules.lp.activityuserdetail.service.IActivityUserDetailService;
import org.jeecg.modules.lp.activityuserinfo.entity.ActivityUserInfo;
import org.jeecg.modules.lp.activityuserinfo.service.IActivityUserInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025/8/4
 */
@Slf4j
@Api(tags = "公益活动-公共接口")
@RestController
@RequestMapping("/lpactivity/public")
public class ActivityPublicController {

    @Autowired
    private IActivityUserInfoService activityUserInfoService;

    @Autowired
    private IActivityBaseService activityBaseService;

    @Autowired
    private IActivityUserDetailService activityUserDetailService;

    /**
     * 活动一览列表
     */
    @AutoLog(value = "公益活动-公共接口-活动一览列表")
    @ApiOperation(value = "公益活动-公共接口-活动一览列表", notes = "公益活动-公共接口-活动一览列表")
    @RequestMapping("/listByMonth")
    public Result<?> listByMonth(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                       @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                       @RequestParam(name = "month", required = false) String month,
                       @RequestParam(name = "xm", required = false) String xm,
                       @RequestParam(name = "jzjg", required = false) String jzjg,
                       @RequestParam(name = "status", required = false) String status
                       ) {
        Page<ActivityUserInfo> pageInfo = new Page<>(pageNo, pageSize);
        QueryWrapper<ActivityUserInfo> queryWrapper = new QueryWrapper<>();
        if (month != null && !month.trim().isEmpty()) {
            queryWrapper.eq("month", month);
        }
        if (xm != null && !xm.trim().isEmpty()) {
            queryWrapper.like("xm", xm);
        }
        if (jzjg != null && !jzjg.trim().isEmpty()) {
            queryWrapper.eq("jzjg", jzjg);
        }
        if (status != null && !status.trim().isEmpty()) {
            queryWrapper.eq("status", status);
        }
        activityUserInfoService.page(pageInfo, queryWrapper);
        return Result.OK(pageInfo);
    }

    /**
     * 活动监测列表
     */
    @AutoLog(value = "公益活动-公共接口-活动监测列表")
    @ApiOperation(value = "公益活动-公共接口-活动监测列表", notes = "公益活动-公共接口-活动监测列表")
    @RequestMapping("/listByActivity")
    public Result<?> listByActivity(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                       @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                       @RequestParam(name = "activityStatus") String activityStatus,
                       @RequestParam(name = "activityDate") String activityDate,
                       @RequestParam(name = "activityProcess") String activityProcess
                       ) {
        Page<ActivityUserInfo> pageInfo = new Page<>(pageNo, pageSize);
        QueryWrapper<ActivityUserInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("activity_status", activityStatus);
        queryWrapper.eq("activity_date", activityDate);
        queryWrapper.eq("activity_process", activityProcess);
        activityUserInfoService.page(pageInfo, queryWrapper);
        return Result.OK(pageInfo);
    }

    /**
     * 劳动基地列表
     */
    @AutoLog(value = "公益活动-公共接口-劳动基地列表")
    @ApiOperation(value = "公益活动-公共接口-劳动基地列表", notes = "公益活动-公共接口-劳动基地列表")
    @RequestMapping("/listByLaborBase")
    public Result<?> listByLaborBase(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                       @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        Page<ActivityUserInfo> pageInfo = new Page<>(pageNo, pageSize);
        QueryWrapper<ActivityUserInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("base_type", "劳动基地");
        activityUserInfoService.page(pageInfo, queryWrapper);
        return Result.OK(pageInfo);
    }

    /**
     * 劳动基地详情
     */
    @AutoLog(value = "公益活动-公共接口-劳动基地详情")
    @ApiOperation(value = "公益活动-公共接口-劳动基地详情", notes = "公益活动-公共接口-劳动基地详情")
    @RequestMapping("/getLaborBaseDetail")
    public Result<?> getLaborBaseDetail(@RequestParam(name = "id") String id) {
        return Result.OK(activityBaseService.getLaborBaseDetail(id));
    }

    /**
     * 调整评价结果
     */
    @AutoLog(value = "公益活动-公共接口-调整评价结果")
    @ApiOperation(value = "公益活动-公共接口-调整评价结果", notes = "公益活动-公共接口-调整评价结果")
    @RequestMapping("/updateEvaluationResult")
    public Result<?> updateEvaluationResult(@RequestParam(name = "id") String id,
                       @RequestParam(name = "evaluationResult") String evaluationResult) {
        return Result.OK(activityUserDetailService.updateEvaluationResult(id, evaluationResult));
    }
}
