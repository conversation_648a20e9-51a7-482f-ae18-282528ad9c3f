package org.jeecg.modules.lp.activitybase.service;

import org.jeecg.modules.lp.activitybase.entity.ActivityBase;
import org.jeecg.modules.lp.activitybase.entity.ActivityBaseDetailVo;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: 公益活动-劳动基地
 * @Author: LiuQC
 * @Date:   2024-04-08
 * @Version: V1.0
 */
public interface IActivityBaseService extends IService<ActivityBase> {

    /**
     * 定时更新参与人数和次数
     */
    void updateActivityBaseCount();

    /**
     * 获取劳动基地详情，包含当前正在劳动的人员信息
     * @param baseId 劳动基地ID
     * @return 劳动基地详情
     */
    ActivityBaseDetailVo getLaborBaseDetail(String baseId);

}
