package org.jeecg.modules.lp.activitybase.controller;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.lp.activitybase.entity.ActivityBase;
import org.jeecg.modules.lp.activitybase.entity.ActivityBaseDetailVo;
import org.jeecg.modules.lp.activitybase.service.IActivityBaseService;
import org.jeecg.modules.lp.activitymanage.entity.ActivityManage;
import org.jeecg.modules.lp.activitymanage.service.IActivityManageService;
import org.jeecg.modules.lp.activitymanage.util.PointsUtil;
import org.jeecg.modules.lp.activityuserdetail.service.IActivityUserDetailService;
import org.jeecg.modules.oss.entity.OSSFile;
import org.jeecg.modules.oss.service.IOSSFileService;
import org.jeecg.modules.system.entity.SysDepart;
import org.jeecg.modules.system.service.ISysDepartService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description: 公益活动-劳动基地
 * @Author: LiuQC
 * @Date: 2024-04-08
 * @Version: V1.0
 */
@Slf4j
@Api(tags = "公益活动-劳动基地")
@RestController
@RequestMapping("/activitybase/activityBase")
public class ActivityBaseController extends JeecgController<ActivityBase, IActivityBaseService> {
    @Autowired
    private IActivityBaseService activityBaseService;

    @Autowired
    private ISysDepartService sysDepartService;

    @Autowired
    private IOSSFileService iossFileService;

    @Autowired
    private IActivityManageService activityManageService;

    @Autowired
    private IActivityUserDetailService activityUserDetailService;

    /**
     * 分页列表查询
     *
     * @param activityBase
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "公益活动-劳动基地-分页列表查询")
    @ApiOperation(value = "公益活动-劳动基地-分页列表查询", notes = "公益活动-劳动基地-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(ActivityBase activityBase,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        if (ObjectUtil.isNotEmpty(activityBase) && ObjectUtil.isNotEmpty(activityBase.getBaseName())) {
            activityBase.setBaseName("*" + activityBase.getBaseName() + "*");
        }
        QueryWrapper<ActivityBase> queryWrapper = QueryGenerator.initQueryWrapper(activityBase, req.getParameterMap());
        Page<ActivityBase> page = new Page<ActivityBase>(pageNo, pageSize);
        IPage<ActivityBase> pageList = activityBaseService.page(page, queryWrapper);
        List<ActivityBase> records = pageList.getRecords();
        if (CollectionUtil.isNotEmpty(records)) {
            for (ActivityBase record : records) {
                List<ActivityManage> activityManageList = activityManageService.list(new QueryWrapper<ActivityManage>().lambda().eq(ActivityManage::getBaseId, record.getId()));
                if (CollectionUtil.isNotEmpty(activityManageList)) {
                    record.setLaunchNumber(activityManageList.size());
                    int sum = activityManageList.stream().mapToInt(ActivityManage::getActPersonNumber).sum();
                    record.setTotalParticipate(sum);
                } else {
                    record.setLaunchNumber(0);
                    record.setTotalParticipate(0);
                }
                if (ObjectUtil.isNotEmpty(record.getPictureIds())) {
                    List<OSSFile> list = iossFileService.list(new QueryWrapper<OSSFile>().lambda().in(OSSFile::getId, Arrays.asList(record.getPictureIds().split(","))));
                    record.setFileList(list);
                }
            }
        }
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param activityBase
     * @return
     */
    @AutoLog(value = "公益活动-劳动基地-添加")
    @ApiOperation(value = "公益活动-劳动基地-添加", notes = "公益活动-劳动基地-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody ActivityBase activityBase) {
        SysDepart sysDepart = sysDepartService.getById(activityBase.getJzjg());
        if (ObjectUtil.isNotEmpty(sysDepart)) {
            activityBase.setJzjgName(sysDepart.getDepartName());
        }
        List<OSSFile> fileList = activityBase.getFileList();
        if (CollectionUtil.isNotEmpty(fileList)) {
            List<String> fileIds = fileList.stream().map(OSSFile::getId).collect(Collectors.toList());
            String join = String.join(",", fileIds);
            activityBase.setPictureIds(join);
        }
        //百度坐标转天地图
        Assert.notBlank(activityBase.getLongitude(), "缺少经度！");
        Assert.notBlank(activityBase.getLatitude(), "缺少纬度！");
        double[] doubles = PointsUtil.bd09towgs84(Double.parseDouble(activityBase.getLongitude()), Double.parseDouble(activityBase.getLatitude()));
        activityBase.setLongitude(String.valueOf(doubles[0]));
        activityBase.setLatitude(String.valueOf(doubles[1]));
        activityBase.setDelFlag(0);
        activityBaseService.save(activityBase);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param activityBase
     * @return
     */
    @AutoLog(value = "公益活动-劳动基地-编辑")
    @ApiOperation(value = "公益活动-劳动基地-编辑", notes = "公益活动-劳动基地-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody ActivityBase activityBase) {
        SysDepart sysDepart = sysDepartService.getById(activityBase.getJzjg());
        if (ObjectUtil.isNotEmpty(sysDepart)) {
            activityBase.setJzjgName(sysDepart.getDepartName());
        }
        List<OSSFile> fileList = activityBase.getFileList();
        if (CollectionUtil.isNotEmpty(fileList)) {
            List<String> fileIds = fileList.stream().map(OSSFile::getId).collect(Collectors.toList());
            String join = String.join(",", fileIds);
            activityBase.setPictureIds(join);
        }
        //百度坐标转天地图
        Assert.notBlank(activityBase.getLongitude(), "缺少经度！");
        Assert.notBlank(activityBase.getLatitude(), "缺少纬度！");
        double[] doubles = PointsUtil.bd09towgs84(Double.parseDouble(activityBase.getLongitude()), Double.parseDouble(activityBase.getLatitude()));
        activityBase.setLongitude(String.valueOf(doubles[0]));
        activityBase.setLatitude(String.valueOf(doubles[1]));
        activityBase.setDelFlag(0);
        activityBaseService.updateById(activityBase);
        //更新相关的活动的经纬度
        activityManageService.updateBaseLongitudeAndLatitude(activityBase.getId(), activityBase.getLongitude(), activityBase.getLatitude());
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "公益活动-劳动基地-通过id删除")
    @ApiOperation(value = "公益活动-劳动基地-通过id删除", notes = "公益活动-劳动基地-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        activityBaseService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "公益活动-劳动基地-批量删除")
    @ApiOperation(value = "公益活动-劳动基地-批量删除", notes = "公益活动-劳动基地-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.activityBaseService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功！");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "公益活动-劳动基地-通过id查询")
    @ApiOperation(value = "公益活动-劳动基地-通过id查询", notes = "公益活动-劳动基地-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        ActivityBase activityBase = activityBaseService.getById(id);
        List<ActivityManage> activityManageList = activityManageService.list(new QueryWrapper<ActivityManage>().lambda().eq(ActivityManage::getBaseId, activityBase.getId()));
        if (CollectionUtil.isNotEmpty(activityManageList)) {
            activityBase.setLaunchNumber(activityManageList.size());
            int sum = activityManageList.stream().mapToInt(ActivityManage::getActPersonNumber).sum();
            activityBase.setTotalParticipate(sum);
        } else {
            activityBase.setLaunchNumber(0);
            activityBase.setTotalParticipate(0);
        }
        if (ObjectUtil.isNotEmpty(activityBase.getPictureIds())) {
            List<OSSFile> list = iossFileService.list(new QueryWrapper<OSSFile>().lambda().in(OSSFile::getId, Arrays.asList(activityBase.getPictureIds().split(","))));
            activityBase.setFileList(list);
        }
        ActivityBase base = activityUserDetailService.calculateBaseScore(activityBase.getId());
        if (ObjectUtil.isNotEmpty(base)) {
            activityBase.setTotalScore(base.getTotalScore());
            activityBase.setAvgScore(base.getAvgScore());
        }
        return Result.OK(activityBase);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param activityBase
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ActivityBase activityBase) {
        return super.exportXls(request, activityBase, ActivityBase.class, "公益活动-劳动基地");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ActivityBase.class);
    }

    /**
     * 获取劳动基地详情，包含当前正在劳动的人员信息
     *
     * @param id 劳动基地ID
     * @return 劳动基地详情
     */
    @AutoLog(value = "公益活动-劳动基地-获取详情")
    @ApiOperation(value = "获取劳动基地详情", notes = "获取劳动基地详情，包含当前正在劳动的人员信息")
    @GetMapping(value = "/getLaborBaseDetail")
    public Result<?> getLaborBaseDetail(@RequestParam(name = "id", required = true) String id) {
        try {
            ActivityBaseDetailVo detailVo = activityBaseService.getLaborBaseDetail(id);
            return Result.OK(detailVo);
        } catch (IllegalArgumentException e) {
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("获取劳动基地详情失败", e);
            return Result.error("获取劳动基地详情失败：" + e.getMessage());
        }
    }

}
